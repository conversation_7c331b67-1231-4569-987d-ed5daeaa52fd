<template>
  <div class="dept-page art-full-height">
    <!-- 搜索栏 -->
    <ArtSearchBar
      v-model="formFilters"
      :items="formItems"
      :showExpand="false"
      @reset="handleReset"
      @search="handleSearch"
    />

    <ElCard class="art-table-card" shadow="never">
      <!-- 表格头部 -->
      <ArtTableHeader :showZebra="false" v-model:columns="columnChecks" @refresh="handleRefresh">
        <template #left>
          <ElButton v-auth="'system:dept:add'" @click="() => handleAdd()" v-ripple>
            新增部门
          </ElButton>
          <ElButton @click="toggleExpand" v-ripple>
            {{ isExpanded ? '收起' : '展开' }}
          </ElButton>
        </template>
      </ArtTableHeader>

      <ArtTable
        ref="tableRef"
        rowKey="deptId"
        :loading="loading"
        :columns="columns"
        :data="filteredTableData"
        :stripe="false"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpanded"
      />

      <!-- 添加或修改部门对话框 -->
      <ElDialog :title="dialogTitle" v-model="dialogVisible" width="600px" align-center>
        <ElForm ref="formRef" :model="form" :rules="rules" label-width="80px">
          <ElRow>
            <ElCol :span="24" v-if="form.parentId !== 0">
              <ElFormItem label="上级部门" prop="parentId">
                <ElTreeSelect
                  v-model="form.parentId"
                  :data="deptOptions"
                  :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                  value-key="deptId"
                  placeholder="选择上级部门"
                  check-strictly
                  clearable
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="部门名称" prop="deptName">
                <ElInput v-model="form.deptName" placeholder="请输入部门名称" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="显示排序" prop="orderNum">
                <ElInputNumber
                  v-model="form.orderNum"
                  controls-position="right"
                  :min="0"
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="负责人" prop="leader">
                <ElInput v-model="form.leader" placeholder="请输入负责人" maxlength="20" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="联系电话" prop="phone">
                <ElInput v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="邮箱" prop="email">
                <ElInput v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
              </ElFormItem>
            </ElCol>
            <ElCol :span="12">
              <ElFormItem label="部门状态">
                <ElRadioGroup v-model="form.status">
                  <ElRadio value="0">正常</ElRadio>
                  <ElRadio value="1">停用</ElRadio>
                </ElRadioGroup>
              </ElFormItem>
            </ElCol>
          </ElRow>
        </ElForm>

        <template #footer>
          <span class="dialog-footer">
            <ElButton @click="dialogVisible = false">取 消</ElButton>
            <ElButton type="primary" @click="submitForm">确 定</ElButton>
          </span>
        </template>
      </ElDialog>
    </ElCard>
  </div>
</template>

<script setup lang="ts">
  import { nextTick, reactive, ref } from 'vue'
  import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
  import { useTableColumns } from '@/composables/useTableColumns'
  import { useAuth } from '@/composables/useAuth'
  import { DeptApi } from '@/api/system/dept'
  import type { Dept } from '@/types/system/dept'
  import type { FormInstance, FormRules } from 'element-plus'
  import ArtButtonTable from '@/components/core/forms/art-button-table/index.vue'

  defineOptions({ name: 'SystemDept' })

  const { hasAuth } = useAuth()

  const loading = ref(false)
  const dialogVisible = ref(false)
  const isExpanded = ref(true)
  const tableRef = ref()
  const formRef = ref<FormInstance>()

  // 定义表单搜索初始值
  const initialSearchState = {
    deptName: '',
    status: ''
  }

  // 响应式表单数据
  const formFilters = reactive({ ...initialSearchState })
  const appliedFilters = reactive({ ...initialSearchState })

  // 部门数据
  const deptList = ref<Dept[]>([])
  const deptOptions = ref<Dept[]>([])

  // 表单数据
  const form = reactive<Dept>({
    deptId: undefined,
    parentId: undefined,
    deptName: '',
    orderNum: 0,
    leader: '',
    phone: '',
    email: '',
    status: '0'
  })

  // 表单验证规则
  const rules = reactive<FormRules>({
    parentId: [{ required: true, message: '上级部门不能为空', trigger: 'blur' }],
    deptName: [{ required: true, message: '部门名称不能为空', trigger: 'blur' }],
    orderNum: [{ required: true, message: '显示排序不能为空', trigger: 'blur' }],
    email: [{ type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }],
    phone: [
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码', trigger: 'blur' }
    ]
  })

  const isEdit = ref(false)
  const dialogTitle = computed(() => (isEdit.value ? '修改部门' : '新增部门'))

  // 重置表单
  const handleReset = () => {
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    getTableData()
  }

  // 搜索处理
  const handleSearch = () => {
    Object.assign(appliedFilters, { ...formFilters })
    getTableData()
  }

  // 表单配置项
  const formItems = computed(() => [
    {
      label: '部门名称',
      key: 'deptName',
      type: 'input',
      props: { clearable: true }
    },
    {
      label: '状态',
      key: 'status',
      type: 'select',
      props: { clearable: true },
      options: [
        { label: '正常', value: '0' },
        { label: '停用', value: '1' }
      ]
    }
  ])

  // 动态列配置
  const { columnChecks, columns } = useTableColumns(() => [
    {
      prop: 'deptName',
      label: '部门名称',
      minWidth: 200
    },
    {
      prop: 'orderNum',
      label: '排序',
      width: 100
    },
    {
      prop: 'status',
      label: '状态',
      width: 100,
      formatter: (row: Dept) => {
        return h(ElTag, { type: row.status === '0' ? 'success' : 'danger' }, () =>
          row.status === '0' ? '正常' : '停用'
        )
      }
    },
    {
      prop: 'createTime',
      label: '创建时间',
      width: 180,
      formatter: (row: Dept) => row.createTime || '--'
    },
    {
      prop: 'operation',
      label: '操作',
      width: 200,
      formatter: (row: Dept) => {
        return h('div', [
          hasAuth('system:dept:edit') &&
            h(ArtButtonTable, {
              type: 'edit',
              onClick: () => handleUpdate(row)
            }),
          hasAuth('system:dept:add') &&
            h(ArtButtonTable, {
              type: 'add',
              onClick: () => handleAdd(row)
            }),
          row.parentId !== 0 &&
            hasAuth('system:dept:remove') &&
            h(ArtButtonTable, {
              type: 'delete',
              onClick: () => handleDelete(row)
            })
        ])
      }
    }
  ])

  onMounted(() => {
    getTableData()
  })

  const getTableData = async () => {
    loading.value = true
    try {
      const response = await DeptApi.getDeptList(appliedFilters)
      // 从嵌套的RuoYi响应格式中提取数据：response.data.data
      const deptData = (response.data as any)?.data || response.data || []
      deptList.value = handleTree(deptData, 'deptId', 'parentId')
    } catch (error) {
      console.error('获取部门列表失败:', error)
      ElMessage.error('获取部门列表失败')
    } finally {
      loading.value = false
    }
  }

  // 过滤后的表格数据
  const filteredTableData = computed(() => {
    if (!appliedFilters.deptName && !appliedFilters.status) {
      return deptList.value
    }

    const filterTree = (items: Dept[]): Dept[] => {
      const results: Dept[] = []

      for (const item of items) {
        const nameMatch =
          !appliedFilters.deptName ||
          item.deptName.toLowerCase().includes(appliedFilters.deptName.toLowerCase())
        const statusMatch = !appliedFilters.status || item.status === appliedFilters.status

        if (item.children && item.children.length > 0) {
          const matchedChildren = filterTree(item.children)
          if (matchedChildren.length > 0) {
            results.push({ ...item, children: matchedChildren })
            continue
          }
        }

        if (nameMatch && statusMatch) {
          results.push({ ...item })
        }
      }

      return results
    }

    return filterTree(deptList.value)
  })

  const handleRefresh = () => {
    console.log('刷新按钮被点击')
    // 重置搜索条件
    Object.assign(formFilters, { ...initialSearchState })
    Object.assign(appliedFilters, { ...initialSearchState })
    getTableData()
  }

  // 展开/收起切换
  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value
    nextTick(() => {
      if (tableRef.value && filteredTableData.value) {
        const processRows = (rows: Dept[]) => {
          rows.forEach((row) => {
            if (row.children && row.children.length > 0) {
              tableRef.value.elTableRef.toggleRowExpansion(row, isExpanded.value)
              processRows(row.children)
            }
          })
        }
        processRows(filteredTableData.value)
      }
    })
  }

  // 新增按钮操作
  const handleAdd = async (row?: Dept) => {
    resetForm()
    try {
      const response = await DeptApi.getDeptList()
      const deptData = (response.data as any)?.data || response.data || []
      deptOptions.value = handleTree(deptData, 'deptId', 'parentId')
    } catch (error) {
      console.error('获取部门选项失败:', error)
      ElMessage.error('获取部门选项失败')
    }

    if (row) {
      form.parentId = row.deptId
    }

    dialogVisible.value = true
    isEdit.value = false
  }

  // 修改按钮操作
  const handleUpdate = async (row: Dept) => {
    resetForm()
    try {
      const [deptResponse, detailResponse] = await Promise.all([
        DeptApi.getDeptListExcludeChild(row.deptId!),
        DeptApi.getDeptDetail(row.deptId!)
      ])

      const deptData = (deptResponse.data as any)?.data || deptResponse.data || []
      const detailData = (detailResponse.data as any)?.data || detailResponse.data

      deptOptions.value = handleTree(deptData, 'deptId', 'parentId')
      Object.assign(form, detailData)

      dialogVisible.value = true
      isEdit.value = true
    } catch (error) {
      console.error('获取部门信息失败:', error)
      ElMessage.error('获取部门信息失败')
    }
  }

  // 删除按钮操作
  const handleDelete = async (row: Dept) => {
    try {
      await ElMessageBox.confirm(`是否确认删除名称为"${row.deptName}"的数据项?`, '系统提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await DeptApi.deleteDept(row.deptId!)
      ElMessage.success('删除成功')
      getTableData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 提交表单
  const submitForm = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        try {
          if (form.deptId) {
            await DeptApi.updateDept(form)
            ElMessage.success('修改成功')
          } else {
            await DeptApi.addDept(form)
            ElMessage.success('新增成功')
          }

          dialogVisible.value = false
          getTableData()
        } catch {
          ElMessage.error(isEdit.value ? '修改失败' : '新增失败')
        }
      }
    })
  }

  // 重置表单
  const resetForm = () => {
    // 逐个重置响应式属性，确保响应式更新
    form.deptId = undefined
    form.parentId = undefined
    form.deptName = ''
    form.orderNum = 0
    form.leader = ''
    form.phone = ''
    form.email = ''
    form.status = '0'

    // 重置表单验证状态
    nextTick(() => {
      formRef.value?.resetFields()
    })
  }

  // 树形数据处理工具函数
  const handleTree = (data: any[], id: string, parentId: string, children = 'children') => {
    const config = {
      id: id || 'id',
      parentId: parentId || 'parentId',
      childrenList: children || 'children'
    }

    const childrenListMap: any = {}
    const nodeIds: any = {}
    const tree = []

    for (const d of data) {
      const parentId = d[config.parentId]
      if (childrenListMap[parentId] == null) {
        childrenListMap[parentId] = []
      }
      nodeIds[d[config.id]] = d
      childrenListMap[parentId].push(d)
    }

    for (const d of data) {
      const parentId = d[config.parentId]
      if (nodeIds[parentId] == null) {
        tree.push(d)
      }
    }

    for (const t of tree) {
      adaptToChildrenList(t)
    }

    function adaptToChildrenList(o: any) {
      if (childrenListMap[o[config.id]] !== null) {
        o[config.childrenList] = childrenListMap[o[config.id]]
      }
      if (o[config.childrenList]) {
        for (const c of o[config.childrenList]) {
          adaptToChildrenList(c)
        }
      }
    }

    return tree
  }
</script>

<style lang="scss" scoped>
  .dept-page {
    .dialog-footer {
      text-align: right;
    }
  }
</style>
